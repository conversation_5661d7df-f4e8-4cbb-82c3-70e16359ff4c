import {Form} from 'antd';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import DescriptionField from '../MCPEdit/BasicInfoContent/DescriptionField';
import ProtocolField from '../MCPEdit/BasicInfoContent/ProtocolField';
import SceneField from '../MCPEdit/BasicInfoContent/SceneField';
import ServerConfigField from '../MCPEdit/BasicInfoContent/ServerConfigField';
import EnglishIdentifierField from './EnglishIdentifierField';
import GlobalVariableField from './GlobalVariableField';
import EnhancedGlobalVariableField from './EnhancedGlobalVariableField';
import MCPIdentifierField from './MCPIdentifierField';
import ServiceNameField from './ServiceNameField';
import Overview from './Overview';
import AuthDescription from './OpenApiFields/AuthDescriptionField';
import AuthTypeField from './OpenApiFields/AuthTypeField';
import MC<PERSON><PERSON>Field from './MCPSpaceField';

interface Props {
    mode: string;
    hidden?: boolean;
}

const BaseContent = ({mode, hidden}: Props) => {
    const spaceId = useMCPWorkspaceId();
    return (
        <div style={{display: hidden ? 'none' : 'block'}}>
            <ServiceNameField />
            {!spaceId && <MCPSpaceField />}
            <EnglishIdentifierField />
            <MCPIdentifierField />
            <DescriptionField />
            {/* <VisibilityField /> */}
            <SceneField />
            <ProtocolField />
            {mode === 'openapi' && (
                <AuthTypeField />
            )}
            {(mode === 'openapi' || mode === 'script') && (
                <Form.Item label="全局变量" name={['serverParams']}>
                    {mode === 'openapi' ? (
                        <EnhancedGlobalVariableField path={['serverParams']} />
                    ) : (
                        <GlobalVariableField path={['serverParams']} />
                    )}
                </Form.Item>
            )}
            {mode === 'openapi' && (
                <AuthDescription />
            )}
            {
                mode === 'external' && (
                    <>
                        <ServerConfigField />
                    </>
                )
            }
            <Overview />
        </div>
    );
};

export default BaseContent;
