/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import {Flex, Form, Input, Select, Space, TableColumnsType} from 'antd';
import {useCallback, useMemo} from 'react';
import {Path} from '@panda-design/path-form';
import {IconAdd} from '@/icons/lucide';
import {IconSubtract} from '@/icons/mcp';
import {RequiredTitle, StyledTable} from '../MCPEdit/ToolsContent/ParamList';

const StyledButton = styled(Button)`
    color: #317ff5 !important;
    position: relative;
    &:hover{
        color: #317ff5 !important;
    }
`;

const typeSelectOptions = [
    {label: 'String', value: 'string'},
    {label: 'Number', value: 'number'},
    {label: 'Boolean', value: 'boolean'},
    {label: 'Array', value: 'array'},
    {label: 'Object', value: 'object'},
    {label: 'Date', value: 'date'},
];

const requiredOptions = [
    {label: '必需', value: true},
    {label: '可选', value: false},
];

interface Param {
    name: string;
    description?: string;
    dataType: string;
    required: boolean;
    isSystem?: boolean;
    hasConflict?: boolean;
}

interface Props {
    value?: Param[];
    onChange?: (value: Param[]) => void;
    path: Path;
    rowKey?: string;
    systemVariables?: Param[];
}


const GlobalVariableField = ({value, onChange, path, rowKey = 'key', systemVariables = []}: Props) => {
    const mergedVariables = useMemo(
        () => {
            const userVariables = value || [];
            const systemVarNames = systemVariables.map(v => v.name);

            const processedUserVars = userVariables.map(userVar => ({
                ...userVar,
                hasConflict: systemVarNames.includes(userVar.name),
            }));

            return [...systemVariables, ...processedUserVars];
        },
        [value, systemVariables]
    );

    const getNewName = useCallback(
        () => {
            const names = mergedVariables?.map(item => item.name);
            let index = 1;
            let name = `key${index}`;
            while (names?.includes(name)) {
                index += 1;
                name = `key${index}`;
            }
            return name;
        },
        [mergedVariables]
    );
    const onAdd = useCallback(
        () => {
            const name = getNewName();
            // 这里必须要有key,key只能用index，不然和serverConfig里的值对不上
            // @ts-ignore
            onChange?.([...(value || []), {name, key: value?.length ?? 0, dataType: 'string', required: false}]);
        },
        [onChange, value, getNewName]
    );

    const onDelete = useCallback(
        (index: number) => {
            const targetVar = mergedVariables[index];
            if (targetVar?.isSystem) {
                return;
            }

            const userVarIndex = index - systemVariables.length;
            if (userVarIndex >= 0 && value) {
                onChange?.([...value.slice(0, userVarIndex), ...value.slice(userVarIndex + 1)]);
            }
        },
        [onChange, value, mergedVariables, systemVariables.length]
    );

    const columns: TableColumnsType<Param> = [
        {
            title: <RequiredTitle>变量名称</RequiredTitle>,
            dataIndex: 'name',
            width: 200,
            render: (_, record, index) => {
                const isSystemVar = record.isSystem;
                const hasConflict = record.hasConflict;
                const actualIndex = isSystemVar ? index : index - systemVariables.length;

                return (
                    <div>
                        <Flex align="center" gap={8}>
                            <Button
                                icon={<IconSubtract />}
                                tooltip={isSystemVar ? '系统变量不可删除' : '删除'}
                                type="text"
                                onClick={() => onDelete(index)}
                                disabled={isSystemVar}
                            />
                            <div style={{flex: 1}}>
                                {isSystemVar ? (
                                    <Input
                                        value={record.name}
                                        disabled
                                        style={{color: '#666'}}
                                    />
                                ) : (
                                    <Form.Item
                                        style={{marginBottom: 0}}
                                        name={[...path, actualIndex, 'name']}
                                        rules={[{required: true, message: '必填项，不可为空'}]}
                                        validateStatus={hasConflict ? 'error' : ''}
                                    >
                                        <Input
                                            placeholder="请输入变量名称"
                                            style={hasConflict ? {borderColor: '#ff4d4f', color: '#ff4d4f'} : {}}
                                        />
                                    </Form.Item>
                                )}
                            </div>
                        </Flex>
                        {hasConflict && !isSystemVar && (
                            <div style={{color: '#ff4d4f', fontSize: '12px', marginTop: '4px'}}>
                                与系统变量冲突
                            </div>
                        )}
                    </div>
                );
            },
        },
        {
            title: <RequiredTitle>是否必须</RequiredTitle>,
            dataIndex: 'required',
            width: 100,
            render: (_, record, index) => {
                const isSystemVar = record.isSystem;
                const actualIndex = isSystemVar ? index : index - systemVariables.length;

                return isSystemVar ? (
                    <Select
                        value={record.required}
                        disabled
                        options={requiredOptions}
                    />
                ) : (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, actualIndex, 'required']}
                        rules={[{required: true, message: '必填项，不可为空'}]}
                    >
                        <Select options={requiredOptions} allowClear placeholder="请选择" />
                    </Form.Item>
                );
            },
        },
        {
            title: <RequiredTitle>类型</RequiredTitle>,
            dataIndex: 'dataType',
            width: 120,
            render: (_, record, index) => {
                const isSystemVar = record.isSystem;
                const actualIndex = isSystemVar ? index : index - systemVariables.length;

                return isSystemVar ? (
                    <Select
                        value={record.dataType}
                        disabled
                        options={typeSelectOptions}
                    />
                ) : (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, actualIndex, 'dataType']}
                        rules={[{required: true, message: '必填项，不可为空'}]}
                    >
                        <Select options={typeSelectOptions} allowClear placeholder="请选择" />
                    </Form.Item>
                );
            },
        },
        {
            title: '描述',
            dataIndex: 'description',
            render: (_, record, index) => {
                const isSystemVar = record.isSystem;
                const actualIndex = isSystemVar ? index : index - systemVariables.length;

                return isSystemVar ? (
                    <Input
                        value={record.description}
                        disabled
                        maxLength={50}
                    />
                ) : (
                    <Form.Item
                        style={{marginBottom: 0}}
                        name={[...path, actualIndex, 'description']}
                        rules={[{max: 50, message: '最长为50字符'}]}
                    >
                        <Input maxLength={50} placeholder="请输入描述" />
                    </Form.Item>
                );
            },
        },

    ];
    return (
        <Space direction="vertical" style={{width: '100%'}}>
            <Flex justify="space-between" align="center">
                <StyledButton type="text" icon={<IconAdd />} onClick={onAdd}>添加变量</StyledButton>
                <span style={{color: '#2D2D2D', fontSize: 12}}>调用该MCP的工具时需传入的全局变量，不会暴露给模型，在脚本中以环境变量形式引用</span>
            </Flex>
            <StyledTable
                rowKey={rowKey}
                dataSource={mergedVariables}
                pagination={{hideOnSinglePage: true}}
                columns={columns}
            />
        </Space>
    );
};

export default GlobalVariableField;

